# 音频问题修复说明

## 修复的问题

### 1. TypeError: n.value.close is not a function
**原因**: GoogleGenerativeAI 的模型对象没有 close() 方法
**修复**: 在 disconnect() 方法中直接设置 session.value = null，而不是调用 close()

### 2. 录音结束后才播放音频，而不是实时播放
**原因**: 
- 当前实现等录音完全结束后才处理音频文件
- sendAudio 方法只是用模拟文本替代真实的语音识别
- 没有实现真正的实时流式传输

**修复**:
1. 修改 sendAudio 方法，区分实时模式和非实时模式
2. 实时模式下直接处理音频流，并模拟实时响应
3. 添加 frameSize 配置启用 onFrameRecorded 回调
4. 改进日志输出，便于调试

## 主要改动

### app/stores/gemini.js
- 修复 disconnect() 方法中的 close() 调用
- 重写 sendAudio() 方法，支持实时和非实时两种模式
- 添加 handleMockResponse() 方法模拟实时响应
- 改进日志输出

### app/pages/interview/interview.vue
- 修改 onFrameRecorded 处理逻辑，直接发送原始音频数据
- 在录音配置中添加 frameSize 参数
- 改进录音停止处理逻辑，区分实时和非实时模式
- 修复 isRecording 变量错误
- 添加更详细的调试日志

## 预期效果

1. 不再出现 "close is not a function" 错误
2. 实时模式下能够接收音频帧并模拟实时响应
3. 非实时模式下正常处理完整音频文件
4. 更清晰的日志输出便于调试

## 测试建议

1. 启动应用并进入面试页面
2. 点击"立即开始"按钮启动实时模式
3. 观察控制台日志，应该看到：
   - "实时模式录音开始，等待音频帧..."
   - "收到音频帧，大小: xxx"
   - "实时发送音频数据，大小: xxx"
   - 偶尔出现 "AI实时响应: xxx"
4. 结束面试时不应该出现错误

## 注意事项

- 当前实现使用模拟数据，真实的语音识别需要集成相应的API
- 实时响应是随机触发的，用于演示实时交互效果
- 需要确保设备支持录音权限和音频帧回调功能
