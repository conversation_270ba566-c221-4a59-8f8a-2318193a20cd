import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { GoogleGenerativeAI } from '@google/generative-ai'

export const useGeminiStore = defineStore('gemini', () => {
  // 状态
  const session = ref(null)
  const isConnected = ref(false)
  const connectionStatus = ref('disconnected')
  const lastMessage = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(3)

  // 面试状态
  const interviewStatus = ref('idle')
  const ephemeralToken = ref('')
  const currentDomain = ref('技术面试')
  const isRealTimeMode = ref(false)

  // 计算属性
  const canSendMessage = computed(() => isConnected.value && session.value)

  // 设置临时令牌
  const setEphemeralToken = (token, domain) => {
    ephemeralToken.value = token
    currentDomain.value = domain
  }

  // 连接Gemini API
  const connect = async () => {
    if (session.value && isConnected.value) {
      return
    }

    if (!ephemeralToken.value) {
      throw new Error('临时令牌不存在')
    }

    connectionStatus.value = 'connecting'

    try {
      const genAI = new GoogleGenerativeAI(ephemeralToken.value)
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' })
      
      session.value = model
      isConnected.value = true
      connectionStatus.value = 'connected'
      reconnectAttempts.value = 0
      interviewStatus.value = 'idle'
      
      console.log('Gemini API连接成功')
    } catch (error) {
      console.error('Gemini API连接失败:', error)
      connectionStatus.value = 'error'
      scheduleReconnect()
      throw error
    }
  }

  // 断开连接
  const disconnect = () => {
    if (session.value) {
      // GoogleGenerativeAI 的模型对象没有 close 方法，直接设置为 null
      session.value = null
    }
    isConnected.value = false
    connectionStatus.value = 'disconnected'
    interviewStatus.value = 'idle'
    ephemeralToken.value = ''
  }

  // 处理内容响应
  const handleResponse = (response) => {
    try {
      const text = response.response.text()
      lastMessage.value = { text }
      
      // 使用语音合成播放回答
      if (text) {
        speakText(text)
        interviewStatus.value = 'speaking'
      }
    } catch (error) {
      console.error('处理响应失败:', error)
    }
  }

  // 语音合成播放文本
  const speakText = (text) => {
    try {
      // 使用uni-app的语音合成API
      uni.createSpeechSynthesisUtterance && uni.createSpeechSynthesisUtterance({
        text: text,
        lang: 'zh-CN',
        rate: 1.0,
        pitch: 1.0,
        volume: 1.0,
        success: () => {
          console.log('语音播放成功')
          interviewStatus.value = isRealTimeMode.value ? 'active' : 'idle'
        },
        fail: (error) => {
          console.error('语音播放失败:', error)
          interviewStatus.value = isRealTimeMode.value ? 'active' : 'idle'
        }
      })
    } catch (error) {
      console.error('语音合成失败:', error)
      interviewStatus.value = isRealTimeMode.value ? 'active' : 'idle'
    }
  }

  // 发送音频（实时流式传输）
  const sendAudio = async (audioData) => {
    if (!canSendMessage.value) return false

    try {
      if (isRealTimeMode.value) {
        // 实时模式：直接处理音频流
        console.log('实时发送音频数据，大小:', audioData.length || audioData.byteLength || 'unknown')
        // 这里应该实现实时语音识别和流式传输
        // 目前先模拟实时响应
        if (Math.random() > 0.95) { // 5%的概率触发响应，模拟语音检测
          const mockResponses = [
            '请继续说明您的技术背景',
            '能详细介绍一下这个项目吗？',
            '您在这个过程中遇到了什么挑战？',
            '很好，请继续'
          ]
          const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)]
          handleMockResponse(randomResponse)
        }
        return true
      } else {
        // 非实时模式：调用语音识别API将音频转换为文本
        console.log('处理完整音频文件，大小:', audioData.length)
        // 模拟语音识别结果
        const mockTexts = [
          '我有三年的前端开发经验，主要使用Vue和React',
          '我最近做了一个电商项目，使用了微服务架构',
          '在项目中我负责用户界面的设计和实现',
          '我熟悉JavaScript、TypeScript和Node.js'
        ]
        const text = mockTexts[Math.floor(Math.random() * mockTexts.length)]
        console.log('模拟语音识别结果:', text)
        return await sendText(text)
      }
    } catch (error) {
      console.error('发送音频失败:', error)
      return false
    }
  }

  // 处理模拟响应（用于实时模式）
  const handleMockResponse = (text) => {
    lastMessage.value = { text }
    console.log('AI实时响应:', text)

    // 使用语音合成播放回答
    if (text) {
      speakText(text)
      interviewStatus.value = 'speaking'
    }
  }

  // 发送文本
  const sendText = async (text) => {
    if (!canSendMessage.value) return false

    try {
      const prompt = `你是一个专业的${currentDomain.value}面试官。请用自然、友好的语调回答以下问题或进行面试对话：${text}`
      const result = await session.value.generateContent(prompt)
      handleResponse(result)
      return true
    } catch (error) {
      console.error('发送文本失败:', error)
      return false
    }
  }

  // 发送打断信号
  const sendInterrupt = () => {
    try {
      // 停止当前语音播放
      uni.stopSpeechSynthesis && uni.stopSpeechSynthesis()
      interviewStatus.value = isRealTimeMode.value ? 'active' : 'idle'
      return true
    } catch (error) {
      console.error('发送打断信号失败:', error)
      return false
    }
  }

  // 播放音频
  const playAudio = (audioData) => {
    try {
      const innerAudioContext = uni.createInnerAudioContext()
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.wav`

      uni.getFileSystemManager().writeFile({
        filePath: tempFilePath,
        data: audioData,
        encoding: 'base64',
        success: () => {
          innerAudioContext.src = tempFilePath
          innerAudioContext.play()

          innerAudioContext.onEnded(() => {
            uni.getFileSystemManager().unlink({
              filePath: tempFilePath,
              success: () => console.log('临时音频文件已删除'),
              fail: (err) => console.warn('删除临时音频文件失败:', err)
            })
            innerAudioContext.destroy()
          })

          innerAudioContext.onError((err) => {
            console.error('音频播放失败:', err)
            innerAudioContext.destroy()
          })
        },
        fail: (err) => {
          console.error('写入音频文件失败:', err)
        }
      })
    } catch (error) {
      console.error('播放音频异常:', error)
    }
  }

  // 开始实时模式
  const startRealTimeMode = () => {
    isRealTimeMode.value = true
    interviewStatus.value = 'active'
  }

  // 结束实时模式
  const endRealTimeMode = () => {
    isRealTimeMode.value = false
    interviewStatus.value = 'idle'
  }

  // 重连机制
  const scheduleReconnect = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts.value) {
      console.log('达到最大重连次数，停止重连')
      return
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 10000)
    
    setTimeout(() => {
      reconnectAttempts.value++
      connect().catch(err => {
        console.error('重连失败:', err)
      })
    }, delay)
  }

  return {
    // 状态
    session,
    isConnected,
    connectionStatus,
    lastMessage,
    interviewStatus,
    ephemeralToken,
    currentDomain,
    isRealTimeMode,
    
    // 计算属性
    canSendMessage,
    
    // 方法
    setEphemeralToken,
    connect,
    disconnect,
    sendAudio,
    sendText,
    sendInterrupt,
    startRealTimeMode,
    endRealTimeMode,
    playAudio
  }
})